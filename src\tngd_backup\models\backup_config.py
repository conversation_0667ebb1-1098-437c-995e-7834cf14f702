#!/usr/bin/env python3
"""
Backup Configuration Models

Data models for backup system configuration including resource management,
streaming settings, and operational parameters.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from datetime import datetime


@dataclass
class ResourceConfig:
    """Resource management configuration."""
    max_threads: int = 4
    memory_threshold_mb: int = 1500
    cpu_threshold_percent: float = 75.0
    cleanup_interval_seconds: int = 180
    resource_check_interval_seconds: int = 30
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResourceConfig':
        """Create ResourceConfig from dictionary."""
        return cls(
            max_threads=data.get('max_threads', 4),
            memory_threshold_mb=data.get('memory_threshold_mb', 1500),
            cpu_threshold_percent=data.get('cpu_threshold_percent', 75.0),
            cleanup_interval_seconds=data.get('cleanup_interval_seconds', 180),
            resource_check_interval_seconds=data.get('resource_check_interval_seconds', 30)
        )


@dataclass
class QueryConfig:
    """Query execution configuration."""
    default_timeout_seconds: int = 1200
    large_table_timeout_seconds: int = 2400
    max_retries: int = 2
    retry_delay_seconds: int = 30
    connection_timeout_seconds: int = 20
    read_timeout_seconds: int = 180
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueryConfig':
        """Create QueryConfig from dictionary."""
        return cls(
            default_timeout_seconds=data.get('default_timeout_seconds', 1200),
            large_table_timeout_seconds=data.get('large_table_timeout_seconds', 2400),
            max_retries=data.get('max_retries', 2),
            retry_delay_seconds=data.get('retry_delay_seconds', 30),
            connection_timeout_seconds=data.get('connection_timeout_seconds', 20),
            read_timeout_seconds=data.get('read_timeout_seconds', 180)
        )


@dataclass
class StreamingConfig:
    """Streaming processor configuration."""
    enabled: bool = True
    default_chunk_size: int = 20000
    max_chunk_size: int = 50000
    min_chunk_size: int = 5000
    streaming_threshold_rows: int = 50000
    memory_threshold_mb: int = 1000
    progress_report_interval: int = 3
    memory_check_interval: int = 2
    enable_adaptive_chunking: bool = True
    chunk_size_adjustment_factor: float = 0.7
    temp_file_cleanup: bool = True
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StreamingConfig':
        """Create StreamingConfig from dictionary."""
        return cls(
            enabled=data.get('enabled', True),
            default_chunk_size=data.get('default_chunk_size', 20000),
            max_chunk_size=data.get('max_chunk_size', 50000),
            min_chunk_size=data.get('min_chunk_size', 5000),
            streaming_threshold_rows=data.get('streaming_threshold_rows', 50000),
            memory_threshold_mb=data.get('memory_threshold_mb', 1000),
            progress_report_interval=data.get('progress_report_interval', 3),
            memory_check_interval=data.get('memory_check_interval', 2),
            enable_adaptive_chunking=data.get('enable_adaptive_chunking', True),
            chunk_size_adjustment_factor=data.get('chunk_size_adjustment_factor', 0.7),
            temp_file_cleanup=data.get('temp_file_cleanup', True)
        )


@dataclass
class UploadPathStructure:
    """Upload path structure configuration."""
    base_path: str = ""
    provider_path: str = "Devo"
    use_month_folders: bool = True
    use_week_folders: bool = True
    use_date_folders: bool = True
    include_table_folders: bool = False

@dataclass
class StorageConfig:
    """Storage and upload configuration."""
    upload_timeout_seconds: int = 3600
    max_upload_retries: int = 3
    retry_delay_seconds: int = 30
    chunk_size_mb: int = 50
    memory_threshold_percent: float = 75.0
    connection_pool_size: int = 3
    verify_integrity: bool = True
    compress_before_upload: bool = True
    upload_path_structure: UploadPathStructure = field(default_factory=UploadPathStructure)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StorageConfig':
        """Create StorageConfig from dictionary."""
        # Handle upload_path_structure
        path_structure_data = data.get('upload_path_structure', {})
        upload_path_structure = UploadPathStructure(
            base_path=path_structure_data.get('base_path', ''),
            provider_path=path_structure_data.get('provider_path', 'Devo'),
            use_month_folders=path_structure_data.get('use_month_folders', True),
            use_week_folders=path_structure_data.get('use_week_folders', True),
            use_date_folders=path_structure_data.get('use_date_folders', True),
            include_table_folders=path_structure_data.get('include_table_folders', False)
        )

        return cls(
            upload_timeout_seconds=data.get('upload_timeout_seconds', 3600),
            max_upload_retries=data.get('max_upload_retries', 3),
            retry_delay_seconds=data.get('retry_delay_seconds', 30),
            chunk_size_mb=data.get('chunk_size_mb', 50),
            memory_threshold_percent=data.get('memory_threshold_percent', 75.0),
            connection_pool_size=data.get('connection_pool_size', 3),
            verify_integrity=data.get('verify_integrity', True),
            compress_before_upload=data.get('compress_before_upload', True),
            upload_path_structure=upload_path_structure
        )


@dataclass
class MonitoringConfig:
    """Monitoring and alerting configuration."""
    enabled: bool = True
    log_level: str = "INFO"
    metrics_collection: bool = True
    health_check_interval_seconds: int = 300
    cpu_warning: float = 80.0
    cpu_critical: float = 95.0
    memory_warning: float = 75.0
    memory_critical: float = 90.0
    thread_warning: int = 100
    thread_critical: int = 200
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitoringConfig':
        """Create MonitoringConfig from dictionary."""
        alert_thresholds = data.get('alert_thresholds', {})
        return cls(
            enabled=data.get('enabled', True),
            log_level=data.get('log_level', 'INFO'),
            metrics_collection=data.get('metrics_collection', True),
            health_check_interval_seconds=data.get('health_check_interval_seconds', 300),
            cpu_warning=alert_thresholds.get('cpu_warning', 80.0),
            cpu_critical=alert_thresholds.get('cpu_critical', 95.0),
            memory_warning=alert_thresholds.get('memory_warning', 75.0),
            memory_critical=alert_thresholds.get('memory_critical', 90.0),
            thread_warning=alert_thresholds.get('thread_warning', 100),
            thread_critical=alert_thresholds.get('thread_critical', 200)
        )


@dataclass
class TableSpecificConfig:
    """Configuration for specific tables."""
    chunk_size: Optional[int] = None
    timeout_seconds: Optional[int] = None
    max_retries: Optional[int] = None
    memory_limit_mb: Optional[int] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableSpecificConfig':
        """Create TableSpecificConfig from dictionary."""
        return cls(
            chunk_size=data.get('chunk_size'),
            timeout_seconds=data.get('timeout_seconds'),
            max_retries=data.get('max_retries'),
            memory_limit_mb=data.get('memory_limit_mb')
        )


@dataclass
class BackupConfig:
    """Main backup configuration containing all settings."""
    version: str = "2.0"
    description: str = "TNGD Backup System Configuration"
    resource: ResourceConfig = field(default_factory=ResourceConfig)
    query: QueryConfig = field(default_factory=QueryConfig)
    streaming: StreamingConfig = field(default_factory=StreamingConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    large_tables: List[str] = field(default_factory=list)
    table_specific: Dict[str, TableSpecificConfig] = field(default_factory=dict)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BackupConfig':
        """Create BackupConfig from dictionary."""
        # Parse table-specific configurations
        table_specific = {}
        for table_name, table_data in data.get('table_specific_settings', {}).items():
            table_specific[table_name] = TableSpecificConfig.from_dict(table_data)
        
        return cls(
            version=data.get('version', '2.0'),
            description=data.get('description', 'TNGD Backup System Configuration'),
            resource=ResourceConfig.from_dict(data.get('resource_management', {})),
            query=QueryConfig.from_dict(data.get('query_settings', {})),
            streaming=StreamingConfig.from_dict(data.get('streaming_config', {})),
            storage=StorageConfig.from_dict(data.get('storage_settings', {})),
            monitoring=MonitoringConfig.from_dict(data.get('monitoring', {})),
            large_tables=data.get('large_tables', []),
            table_specific=table_specific
        )
    
    @classmethod
    def from_config_manager(cls, config_manager) -> 'BackupConfig':
        """Create BackupConfig from ConfigManager instance."""
        # This would integrate with the existing ConfigManager
        # For now, return default config
        return cls()
    
    def get_table_config(self, table_name: str) -> Optional[TableSpecificConfig]:
        """Get configuration for a specific table."""
        return self.table_specific.get(table_name)
    
    def is_large_table(self, table_name: str) -> bool:
        """Check if table is configured as large table."""
        return table_name in self.large_tables
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'version': self.version,
            'description': self.description,
            'resource_management': {
                'max_threads': self.resource.max_threads,
                'memory_threshold_mb': self.resource.memory_threshold_mb,
                'cpu_threshold_percent': self.resource.cpu_threshold_percent,
                'cleanup_interval_seconds': self.resource.cleanup_interval_seconds,
                'resource_check_interval_seconds': self.resource.resource_check_interval_seconds
            },
            'query_settings': {
                'default_timeout_seconds': self.query.default_timeout_seconds,
                'large_table_timeout_seconds': self.query.large_table_timeout_seconds,
                'max_retries': self.query.max_retries,
                'retry_delay_seconds': self.query.retry_delay_seconds,
                'connection_timeout_seconds': self.query.connection_timeout_seconds,
                'read_timeout_seconds': self.query.read_timeout_seconds
            },
            'streaming_config': {
                'enabled': self.streaming.enabled,
                'default_chunk_size': self.streaming.default_chunk_size,
                'max_chunk_size': self.streaming.max_chunk_size,
                'min_chunk_size': self.streaming.min_chunk_size,
                'streaming_threshold_rows': self.streaming.streaming_threshold_rows,
                'memory_threshold_mb': self.streaming.memory_threshold_mb,
                'progress_report_interval': self.streaming.progress_report_interval,
                'memory_check_interval': self.streaming.memory_check_interval,
                'enable_adaptive_chunking': self.streaming.enable_adaptive_chunking,
                'chunk_size_adjustment_factor': self.streaming.chunk_size_adjustment_factor,
                'temp_file_cleanup': self.streaming.temp_file_cleanup
            },
            'large_tables': self.large_tables,
            'table_specific_settings': {
                table_name: {
                    'chunk_size': config.chunk_size,
                    'timeout_seconds': config.timeout_seconds,
                    'max_retries': config.max_retries,
                    'memory_limit_mb': config.memory_limit_mb
                }
                for table_name, config in self.table_specific.items()
                if any([config.chunk_size, config.timeout_seconds, config.max_retries, config.memory_limit_mb])
            }
        }
