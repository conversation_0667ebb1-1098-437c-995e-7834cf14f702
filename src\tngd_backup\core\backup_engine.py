#!/usr/bin/env python3
"""
TNGD Backup Engine

Main backup orchestration engine that coordinates all backup operations
with improved resource management, error handling, and monitoring.

This module serves as the primary interface for backup operations,
integrating all core components into a cohesive system.
"""

import os
import sys
import time
import json
import logging
import gc
import os
import calendar
import tarfile
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# Import core components
from .config_manager import ConfigManager
from .devo_client import DevoClient
from .storage_manager import StorageManager
from .email_service import EmailService
from .streaming_processor import StreamingDataProcessor, StreamingConfig
from .thread_manager import get_thread_manager, managed_thread_pool, log_thread_metrics

# Import utilities
from ..utils.monitoring import ResourceMonitor
from ..models.checkpoint import BackupCheckpoint
from ..models.backup_config import BackupConfig


class CheckpointHandler:
    """Simple checkpoint handler for backup progress"""

    def __init__(self):
        self.checkpoints = {}

    def save_checkpoint(self, checkpoint: BackupCheckpoint):
        """Save a checkpoint"""
        self.checkpoints[checkpoint.backup_id] = checkpoint

    def load_checkpoint(self, backup_id: str) -> Optional[BackupCheckpoint]:
        """Load a checkpoint by backup ID"""
        return self.checkpoints.get(backup_id)


class BackupEngine:
    """
    Main backup engine for TNGD system.
    
    Coordinates all backup operations with advanced features:
    - Resource management and monitoring
    - Checkpoint system for recovery
    - Thread pool management
    - Comprehensive error handling
    - Progress tracking and reporting
    """
    
    def __init__(self, dates: Optional[List[datetime]] = None, config_path: Optional[str] = None):
        """
        Initialize the backup engine.
        
        Args:
            dates: List of dates to backup (defaults to today)
            config_path: Path to configuration file
        """
        self.dates = dates or [datetime.now()]
        self.backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Setup logging
        self.log_file_path = self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize configuration
        self.config_manager = ConfigManager(config_path)
        self.backup_config = BackupConfig.from_config_manager(self.config_manager)
        
        # Initialize checkpoint system
        self.checkpoint_handler = CheckpointHandler()
        self.checkpoint = BackupCheckpoint(
            backup_id=self.backup_id,
            start_time=datetime.now(),
            dates=[d.strftime('%Y-%m-%d') for d in self.dates],
            tables=[],
            completed_tables={},
            failed_tables={}
        )
        
        # Initialize components
        self.devo_client = None
        self.storage_manager = None
        self.email_service = None
        self.streaming_processor = None
        self.resource_monitor = ResourceMonitor()
        
        # Resource management
        self.thread_manager = get_thread_manager()
        self.temp_files = []
        
        # Performance tracking
        self.start_time = None
        self.metrics = {
            'total_tables': 0,
            'completed_tables': 0,
            'failed_tables': 0,
            'total_rows': 0,
            'total_duration': 0
        }
        
        self.logger.info(f"BackupEngine initialized: {self.backup_id}")

    def _generate_upload_path(self, target_date: datetime, table_name: str, filename: str) -> str:
        """
        Generate the upload path based on configuration settings.

        Args:
            target_date: The date for the backup
            table_name: Name of the table being backed up
            filename: Name of the file to upload

        Returns:
            Complete OSS path for the upload
        """
        # Get upload path structure configuration
        path_config = self.backup_config.storage.upload_path_structure

        # Start with base path (only if not empty)
        path_parts = []
        if path_config.base_path:
            path_parts.append(path_config.base_path)

        # Add provider path
        if path_config.provider_path:
            path_parts.append(path_config.provider_path)

        # Add month folder if enabled
        if path_config.use_month_folders:
            month_name = calendar.month_name[target_date.month]
            path_parts.append(month_name)

        # Add week folder if enabled
        if path_config.use_week_folders:
            # Calculate week number within the month
            first_day_of_month = target_date.replace(day=1)
            week_of_month = ((target_date.day - 1) // 7) + 1
            path_parts.append(f"week {week_of_month}")

        # Add date folder if enabled
        if path_config.use_date_folders:
            date_str = target_date.strftime('%Y-%m-%d')
            path_parts.append(date_str)

        # Add table folder if enabled
        if path_config.include_table_folders:
            path_parts.append(table_name)

        # Add filename
        path_parts.append(filename)

        # Join all parts with forward slashes (OSS uses forward slashes)
        upload_path = '/'.join(path_parts)

        return upload_path

    def _create_table_archive(self, chunk_files: List[str], archive_name: str, output_dir: str) -> str:
        """
        Create a single tar.gz archive containing all chunk files for a table.

        Args:
            chunk_files: List of chunk file paths
            archive_name: Name for the archive file
            output_dir: Directory to create the archive in

        Returns:
            Path to the created archive
        """
        archive_path = os.path.join(output_dir, archive_name)

        try:
            self.logger.info(f"Creating archive {archive_name} with {len(chunk_files)} chunk files")

            with tarfile.open(archive_path, 'w:gz') as tar:
                for chunk_file in chunk_files:
                    if os.path.exists(chunk_file):
                        # Add file to archive with just the filename (not full path)
                        arcname = os.path.basename(chunk_file)
                        tar.add(chunk_file, arcname=arcname)
                        self.logger.debug(f"Added {arcname} to archive")
                    else:
                        self.logger.warning(f"Chunk file not found: {chunk_file}")

            # Clean up individual chunk files after archiving
            for chunk_file in chunk_files:
                try:
                    if os.path.exists(chunk_file):
                        os.remove(chunk_file)
                        self.logger.debug(f"Cleaned up chunk file: {chunk_file}")
                except Exception as e:
                    self.logger.warning(f"Failed to clean up chunk file {chunk_file}: {e}")

            archive_size = os.path.getsize(archive_path) / (1024 * 1024)  # Size in MB
            self.logger.info(f"Archive created successfully: {archive_name} ({archive_size:.2f} MB)")

            return archive_path

        except Exception as e:
            self.logger.error(f"Failed to create archive {archive_name}: {e}")
            # Clean up partial archive if it exists
            if os.path.exists(archive_path):
                try:
                    os.remove(archive_path)
                except:
                    pass
            raise

    def _setup_logging(self) -> str:
        """Setup logging configuration."""
        # Create logs directory in data folder
        logs_dir = Path("data/logs")
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate log filename
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        date_info = ""
        if self.dates:
            if len(self.dates) == 1:
                date_info = f"_{self.dates[0].strftime('%Y-%m-%d')}"
            else:
                start_date = self.dates[0].strftime('%Y-%m-%d')
                end_date = self.dates[-1].strftime('%Y-%m-%d')
                date_info = f"_{start_date}_to_{end_date}"
        
        log_filename = f"tngd_backup_{timestamp}{date_info}.log"
        log_file_path = logs_dir / log_filename
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file_path, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return str(log_file_path)
    
    def initialize_components(self) -> bool:
        """
        Initialize all backup components.
        
        Returns:
            bool: True if initialization successful
        """
        try:
            self.logger.info("Initializing backup components...")
            
            # Initialize Devo client
            self.devo_client = DevoClient()
            self.logger.info("Devo client initialized")
            
            # Initialize storage manager
            self.storage_manager = StorageManager(self.config_manager)
            self.logger.info("Storage manager initialized")
            
            # Initialize email service
            try:
                self.email_service = EmailService()
                self.logger.info("Email service initialized")
            except Exception as e:
                self.logger.warning(f"Email service initialization failed: {e}")
                self.email_service = None
            
            # Initialize streaming processor
            streaming_config = StreamingConfig(
                default_chunk_size=self.backup_config.streaming.default_chunk_size,
                streaming_threshold_rows=self.backup_config.streaming.streaming_threshold_rows,
                memory_threshold_mb=self.backup_config.resource.memory_threshold_mb
            )
            self.streaming_processor = StreamingDataProcessor(streaming_config, self._log_step)
            self.logger.info("Streaming processor initialized")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Component initialization failed: {e}")
            return False
    
    def load_tables(self) -> List[str]:
        """Load table list from configuration."""
        try:
            tables_file = Path("config/tables.json")
            if tables_file.exists():
                with open(tables_file, 'r') as f:
                    tables = json.load(f)
                self.logger.info(f"Loaded {len(tables)} tables from {tables_file}")
                return tables
            else:
                # Fallback tables
                fallback_tables = [
                    'my.app.tngd.polardb',
                    'cloud.office365.management.exchange',
                    'firewall.fortinet.traffic.forward'
                ]
                self.logger.warning(f"Using fallback table list: {len(fallback_tables)} tables")
                return fallback_tables
        except Exception as e:
            self.logger.error(f"Failed to load tables: {e}")
            raise
    
    def run_backup(self) -> Dict[str, Any]:
        """
        Run the complete backup process.
        
        Returns:
            Dict containing backup results and metrics
        """
        self.start_time = time.time()
        self.logger.info("=== TNGD BACKUP ENGINE STARTED ===")
        self.logger.info(f"Backup ID: {self.backup_id}")
        self.logger.info(f"Log file: {self.log_file_path}")
        self.logger.info(f"Dates to process: {len(self.dates)}")
        
        results = {
            'backup_id': self.backup_id,
            'status': 'completed',
            'start_time': self.start_time,
            'dates': [d.strftime('%Y-%m-%d') for d in self.dates],
            'metrics': self.metrics,
            'error': None
        }
        
        try:
            # Initialize components
            if not self.initialize_components():
                raise Exception("Component initialization failed")
            
            # Load tables
            tables = self.load_tables()
            self.metrics['total_tables'] = len(tables) * len(self.dates)
            
            # Start resource monitoring
            self.resource_monitor.start_monitoring()
            
            # Process each date
            for i, target_date in enumerate(self.dates, 1):
                self.logger.info(f"Processing date {i}/{len(self.dates)}: {target_date.strftime('%Y-%m-%d')}")
                
                date_result = self._backup_date(tables, target_date)
                self.metrics['completed_tables'] += date_result.get('completed', 0)
                self.metrics['failed_tables'] += date_result.get('failed', 0)
                self.metrics['total_rows'] += date_result.get('total_rows', 0)
            
            self.logger.info("=== BACKUP COMPLETED SUCCESSFULLY ===")
            
        except Exception as e:
            results['status'] = 'failed'
            results['error'] = str(e)
            self.logger.error(f"Backup failed: {e}")
        
        finally:
            # Cleanup and finalization
            self._finalize_backup(results)
        
        return results
    
    def _backup_date(self, tables: List[str], target_date: datetime) -> Dict[str, Any]:
        """Backup all tables for a specific date."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Initialize date result
        date_result = {
            'date': date_str,
            'completed': 0,
            'failed': 0,
            'no_data': 0,
            'total_rows': 0,
            'table_results': []
        }

        self.logger.info(f"Starting backup for date: {date_str}")
        self.logger.info(f"Tables to process: {len(tables)}")

        # Create output directory for this date
        output_dir = Path("data/exports") / date_str
        output_dir.mkdir(parents=True, exist_ok=True)

        for table in tables:
            table_start_time = time.time()
            try:
                self.logger.info(f"Processing table: {table}")

                # Build where clause for the specific date
                where_clause = f"eventdate = '{date_str}'"

                # Query table data using Devo client
                query_result = self.devo_client.query_table_to_file(
                    table_name=table,
                    where_clause=where_clause,
                    output_dir=str(output_dir),
                    chunk_size=50000,  # Process in 50k row chunks
                    timeout=1800,  # 30 minute timeout per table
                    max_retries=3
                )

                table_duration = time.time() - table_start_time

                if query_result.get('total_rows', 0) > 0:
                    # Data found - create single archive for all chunks
                    chunk_files = query_result.get('file_paths', [])
                    upload_success = True

                    if chunk_files:
                        try:
                            # Create a single archive containing all chunk files for this table
                            archive_name = f"{table.replace('.', '_')}_{date_str}.tar.gz"
                            archive_path = self._create_table_archive(chunk_files, archive_name, str(output_dir))
                            self.temp_files.append(archive_path)

                            # Upload the single archive
                            oss_path = self._generate_upload_path(target_date, table, Path(archive_path).name)
                            success, upload_details = self.storage_manager.upload_file(archive_path, oss_path)

                            if not success:
                                upload_success = False
                                self.logger.error(f"Failed to upload {archive_path}: {upload_details.get('error', 'Unknown error')}")
                            else:
                                self.logger.info(f"Successfully uploaded {Path(archive_path).name} containing {len(chunk_files)} chunks")

                        except Exception as upload_error:
                            upload_success = False
                            self.logger.error(f"Upload error for table {table}: {upload_error}")
                    else:
                        self.logger.warning(f"No chunk files found for table {table}")

                    # Record results
                    table_result = {
                        'table_name': table,
                        'status': 'completed' if upload_success else 'upload_failed',
                        'rows': query_result.get('total_rows', 0),
                        'duration': table_duration,
                        'error': None if upload_success else 'Upload failed'
                    }

                    date_result['completed'] += 1 if upload_success else 0
                    date_result['failed'] += 0 if upload_success else 1
                    date_result['total_rows'] += query_result.get('total_rows', 0)

                else:
                    # No data found for this date
                    table_result = {
                        'table_name': table,
                        'status': 'no_data',
                        'rows': 0,
                        'duration': table_duration,
                        'error': None
                    }
                    date_result['no_data'] += 1

                date_result['table_results'].append(table_result)

            except Exception as e:
                table_duration = time.time() - table_start_time
                self.logger.error(f"Failed to process table {table}: {e}")
                table_result = {
                    'table_name': table,
                    'status': 'failed',
                    'rows': 0,
                    'duration': table_duration,
                    'error': str(e)
                }
                date_result['table_results'].append(table_result)
                date_result['failed'] += 1

        self.logger.info(f"Date {date_str} backup completed: {date_result['completed']} successful, {date_result['failed']} failed, {date_result['no_data']} no data")
        return date_result


    def _log_step(self, step: str, message: str, level: str = "INFO"):
        """Enhanced logging with resource monitoring."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {step}: {message}"
        
        if level == "ERROR":
            self.logger.error(formatted_message)
        elif level == "WARNING":
            self.logger.warning(formatted_message)
        else:
            self.logger.info(formatted_message)
    
    def _finalize_backup(self, results: Dict[str, Any]):
        """Finalize backup process with cleanup and reporting."""
        try:
            # Stop resource monitoring
            self.resource_monitor.stop_monitoring()
            
            # Calculate final metrics
            if self.start_time:
                self.metrics['total_duration'] = time.time() - self.start_time
            
            # Cleanup temp files
            self._cleanup_temp_files()
            
            # Send email summary
            if self.email_service:
                try:
                    self.email_service.send_backup_summary(results)
                    self.logger.info("Backup summary email sent")
                except Exception as e:
                    self.logger.warning(f"Failed to send email: {e}")
            
            # Final resource cleanup
            gc.collect()
            log_thread_metrics()
            
        except Exception as e:
            self.logger.error(f"Finalization error: {e}")
    
    def _cleanup_temp_files(self):
        """Clean up temporary files."""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.logger.debug(f"Cleaned up temp file: {temp_file}")
            except Exception as e:
                self.logger.warning(f"Failed to cleanup {temp_file}: {e}")
        
        self.temp_files.clear()


def main():
    """Main entry point for backup engine."""
    import argparse
    
    parser = argparse.ArgumentParser(description="TNGD Backup Engine")
    parser.add_argument("dates", nargs="*", help="Dates to backup (YYYY-MM-DD format)")
    parser.add_argument("--config", help="Configuration file path")
    
    args = parser.parse_args()
    
    # Parse dates
    dates = []
    if args.dates:
        for date_str in args.dates:
            try:
                dates.append(datetime.strptime(date_str, '%Y-%m-%d'))
            except ValueError:
                print(f"Invalid date format: {date_str}. Use YYYY-MM-DD")
                sys.exit(1)
    
    # Create and run backup engine
    engine = BackupEngine(dates, args.config)
    results = engine.run_backup()
    
    # Exit with appropriate code
    sys.exit(0 if results['status'] == 'completed' else 1)


if __name__ == "__main__":
    main()
