{"version": "2.0", "description": "TNGD Backup System - Development Configuration", "resource_management": {"max_threads": 2, "memory_threshold_mb": 1000, "cpu_threshold_percent": 70, "disk_threshold_percent": 80, "cleanup_interval_seconds": 120, "resource_check_interval_seconds": 15}, "query_settings": {"default_timeout_seconds": 600, "large_table_timeout_seconds": 1200, "max_retries": 1, "retry_delay_seconds": 15, "connection_timeout_seconds": 15, "read_timeout_seconds": 120}, "streaming_config": {"enabled": true, "default_chunk_size": 5000, "max_chunk_size": 10000, "min_chunk_size": 1000, "streaming_threshold_rows": 10000, "memory_threshold_mb": 500, "progress_report_interval": 1, "memory_check_interval": 1, "enable_adaptive_chunking": true, "chunk_size_adjustment_factor": 0.5, "temp_file_cleanup": true}, "storage_settings": {"upload_timeout_seconds": 1800, "max_upload_retries": 2, "retry_delay_seconds": 15, "chunk_size_mb": 10, "memory_threshold_percent": 70, "connection_pool_size": 2, "verify_integrity": true, "compress_before_upload": true, "upload_path_structure": {"base_path": "", "provider_path": "Devo", "use_month_folders": true, "use_week_folders": true, "use_date_folders": true, "include_table_folders": false}}, "monitoring": {"enabled": true, "log_level": "DEBUG", "metrics_collection": true, "health_check_interval_seconds": 60, "alert_thresholds": {"cpu_warning": 70, "cpu_critical": 85, "memory_warning": 70, "memory_critical": 85, "thread_warning": 50, "thread_critical": 100}}, "large_tables": ["cef0.zscaler.nssweblog", "cloud.alibaba.log_service.events", "firewall.fortinet.traffic.forward"], "table_specific_settings": {"cef0.zscaler.nssweblog": {"chunk_size": 1000, "timeout_seconds": 1200, "max_retries": 2, "memory_limit_mb": 200}, "cloud.alibaba.log_service.events": {"chunk_size": 2000, "timeout_seconds": 1200, "max_retries": 2, "memory_limit_mb": 300}, "firewall.fortinet.traffic.forward": {"chunk_size": 1500, "timeout_seconds": 900, "max_retries": 2, "memory_limit_mb": 250}}, "error_handling": {"max_consecutive_failures": 2, "failure_cooldown_minutes": 2, "auto_skip_problematic_tables": false, "detailed_error_logging": true, "error_notification_threshold": 1}, "recovery": {"checkpoint_enabled": true, "checkpoint_interval_minutes": 5, "auto_resume": true, "max_resume_attempts": 2, "resume_delay_minutes": 1}, "performance_optimizations": {"connection_pooling": false, "query_caching": false, "parallel_uploads": false, "memory_mapping": false, "compression_level": 1, "buffer_size_kb": 32}, "logging": {"level": "DEBUG", "max_file_size_mb": 50, "backup_count": 3, "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s", "separate_error_log": true, "log_rotation": true}}