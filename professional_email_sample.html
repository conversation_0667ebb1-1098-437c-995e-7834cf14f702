<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); overflow: hidden; }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px 20px; text-align: center; position: relative; }
        .header::after { content: ''; position: absolute; bottom: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #3498db, #2980b9, #3498db); }
        .status-success { color: #27ae60; font-weight: bold; }
        .status-failed { color: #e74c3c; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 20px; }
        .summary-card { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #e9ecef; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
        .summary-card h3 { margin: 0 0 12px 0; color: #495057; font-size: 13px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }
        .summary-card .number { font-size: 28px; font-weight: 700; margin-bottom: 5px; }
        .number-default { color: #2c3e50; }
        .number-success { color: #27ae60; }
        .number-failed { color: #e74c3c; }
        .number-warning { color: #f39c12; }
        .number-info { color: #3498db; }
        .table-results { margin: 30px 20px; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
        .table-results h3 { color: #2c3e50; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding: 20px 20px 0 20px; }
        .table-results table { width: 100%; border-collapse: collapse; }
        .table-results th, .table-results td { padding: 12px 20px; text-align: left; border-bottom: 1px solid #e9ecef; }
        .table-results th { background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); color: white; font-weight: 600; font-size: 14px; text-transform: uppercase; letter-spacing: 0.5px; }
        .table-results tr:nth-child(even) { background-color: #f8f9fa; }
        .table-results tr:hover { background-color: #e3f2fd; }
        .footer { margin-top: 40px; padding: 25px 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 3px solid #3498db; color: #6c757d; font-size: 12px; text-align: center; }
        .error-details { background-color: #fdf2f2; border: 1px solid #f5c6cb; padding: 15px; border-radius: 6px; margin: 10px 0; }
        .success-icon { color: #27ae60; }
        .error-icon { color: #e74c3c; }
        .warning-icon { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Email Content Wrapper -->
        <div class="header">
            <!-- Placeholder for Techlab Security Logo -->
            <div style="width: 45px; height: 45px; background: rgba(255,255,255,0.2); border-radius: 8px; margin: 0 auto 25px auto; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px;">LOGO</div>
            <h1 style="margin: 0 0 15px 0; font-size: 28px; font-weight: 600; letter-spacing: 0.5px;">TNGD Backup System Report</h1>
            <div style="background-color: rgba(255,255,255,0.1); padding: 15px 25px; border-radius: 8px; margin: 15px auto; max-width: 500px;">
                <p style="margin: 0 0 8px 0; font-size: 14px; opacity: 0.9;">Backup completed on 2025-07-01 23:28:01</p>
                <p style="margin: 0 0 8px 0; font-size: 14px; opacity: 0.9;">
                    Backup Date(s):
                    <span style="background-color: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 4px; margin: 0 2px;">2025-06-30</span>,
                    <span style="background-color: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 4px; margin: 0 2px;">2025-07-01</span>
                </p>
                <p style="margin: 0; font-size: 16px; font-weight: 600;" class="status-success">
                    Status: COMPLETED
                </p>
            </div>
        </div>

        <!-- Main Content Area -->
        <div style="padding: 0 0 20px 0;">
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Dates Processed</h3>
                <div class="number number-info">2</div>
            </div>
            <div class="summary-card">
                <h3>Tables Processed</h3>
                <div class="number number-info">6</div>
            </div>
            <div class="summary-card">
                <h3>Successful</h3>
                <div class="number number-success">5</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number number-failed">1</div>
            </div>
            <div class="summary-card">
                <h3>No Data</h3>
                <div class="number number-warning">0</div>
            </div>
            <div class="summary-card">
                <h3>Total Rows</h3>
                <div class="number number-default">30,000</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="number number-default">9.0m</div>
            </div>
        </div>

        <div class="table-results">
            <h3>📅 Results for 2025-07-01</h3>
            <table>
                <thead>
                    <tr>
                        <th>Table Name</th>
                        <th>Status</th>
                        <th>Rows</th>
                        <th>Duration (s)</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>my.app.tngd.waf</td>
                        <td><span class="success-icon">✅</span> completed</td>
                        <td>8,500</td>
                        <td>65.2</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>my.app.tngd.actiontraillinux</td>
                        <td><span class="success-icon">✅</span> completed</td>
                        <td>6,500</td>
                        <td>45.8</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>cloud.office365.management.exchange</td>
                        <td><span class="error-icon">❌</span> failed</td>
                        <td>0</td>
                        <td>12.5</td>
                        <td>Connection timeout</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="error-details">
            <h3>Error Details</h3>
            <p><strong>cloud.office365.management.exchange:</strong> Connection timeout</p>
        </div>
        </div>
        <!-- End Main Content Area -->

        <div class="footer">
            <p style="margin: 0 0 8px 0; font-weight: 600;">This report was generated automatically by Techlab Automation Team.</p>
            <p style="margin: 0; opacity: 0.8;">Report generated at: 2025-07-01 23:28:01</p>
        </div>
    </div>
</body>
</html>