
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; text-align: center; }
        .header-icon { width: 32px; height: 32px; margin-bottom: 10px; }
        .status-success { color: #27ae60; font-weight: bold; }
        .status-failed { color: #e74c3c; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px; }
        .summary-card { background-color: #ecf0f1; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #2c3e50; }
        .summary-card .number { font-size: 24px; font-weight: bold; color: #3498db; }
        .table-results { margin: 20px 0; }
        .table-results table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table-results th, .table-results td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table-results th { background-color: #34495e; color: white; }
        .table-results tr:nth-child(even) { background-color: #f8f9fa; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d; font-size: 12px; text-align: center; }
        .error-details { background-color: #fdf2f2; border: 1px solid #f5c6cb; padding: 15px; border-radius: 6px; margin: 10px 0; }
        .success-icon { color: #27ae60; }
        .error-icon { color: #e74c3c; }
        .warning-icon { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            
            <img src="data:image/png;base64,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" alt="Icon" style="width: 32px; height: 32px; margin-bottom: 10px;" />
            
            <h1>TNGD Backup System Report</h1>
            <p>Backup completed on 2025-07-01 23:17:15</p>
            <p class="status-success">
                Status: COMPLETED
            </p>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Dates Processed</h3>
                <div class="number">1</div>
            </div>
            <div class="summary-card">
                <h3>Tables Processed</h3>
                <div class="number">3</div>
            </div>
            <div class="summary-card">
                <h3>Successful</h3>
                <div class="number">2</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number">1</div>
            </div>
            <div class="summary-card">
                <h3>No Data</h3>
                <div class="number">0</div>
            </div>
            <div class="summary-card">
                <h3>Total Rows</h3>
                <div class="number">15,000</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="number">3.0m</div>
            </div>
        </div>
        
        
        <div class="table-results">
            <h2>Detailed Results by Date</h2>
            
            <h3>Date: 2025-07-01</h3>
            <table>
                <thead>
                    <tr>
                        <th>Table Name</th>
                        <th>Status</th>
                        <th>Rows</th>
                        <th>Duration</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody>
                    
                    <tr>
                        <td>my.app.tngd.waf</td>
                        <td>
                            
                                <span class="status-success">Completed</span>
                            
                        </td>
                        <td>8,500</td>
                        <td>65.2s</td>
                        <td>-</td>
                    </tr>
                    
                    <tr>
                        <td>my.app.tngd.actiontraillinux</td>
                        <td>
                            
                                <span class="status-success">Completed</span>
                            
                        </td>
                        <td>6,500</td>
                        <td>45.8s</td>
                        <td>-</td>
                    </tr>
                    
                    <tr>
                        <td>cloud.office365.management.exchange</td>
                        <td>
                            
                                <span class="status-failed">Failed</span>
                            
                        </td>
                        <td>0</td>
                        <td>12.5s</td>
                        <td>Connection timeout</td>
                    </tr>
                    
                </tbody>
            </table>
            
        </div>
        
        
        
        <div class="error-details">
            <h3>Error Details</h3>
            
            <p><strong>cloud.office365.management.exchange:</strong> Connection timeout</p>
            
        </div>
        
        
        <div class="footer">
            <p>This report was generated automatically by Techlab Automation Team.</p>
            <p>Report generated at: 2025-07-01 23:17:15</p>
        </div>
    </div>
</body>
</html>
        