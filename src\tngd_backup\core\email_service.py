#!/usr/bin/env python3
"""
Email Service Module

This module provides email functionality for the TNGD backup system.
It handles SMTP configuration, email formatting, and sending functionality
using credentials from .env file.

Features:
- SMTP email sending with Gmail support
- HTML email templates using Jinja2
- Backup summary report generation
- Error handling and logging
- Mock mode for testing
"""

import os
import smtplib
import logging
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, Any, List, Optional
from jinja2 import Template

# Configure logging
logger = logging.getLogger(__name__)


class EmailServiceError(Exception):
    """Custom exception for email service errors."""
    pass


class EmailService:
    """
    Email service for sending backup summary reports.
    
    This service handles SMTP configuration, email formatting, and sending
    functionality using credentials from environment variables.
    """
    
    def __init__(self):
        """Initialize the email service with configuration from environment."""
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.sender_email = os.getenv('SMTP_SENDER')
        self.receiver_email = os.getenv('SMTP_RECEIVER')
        self.smtp_password = os.getenv('SMTP_PASSWORD')
        self.mock_mode = os.getenv('SMTP_MOCK_MODE', 'true').lower() == 'true'
        
        # Validate required configuration
        self._validate_config()
        
        logger.info(f"Email service initialized - Mock mode: {self.mock_mode}")
        if not self.mock_mode:
            logger.info(f"SMTP server: {self.smtp_server}:{self.smtp_port}")
            logger.info(f"Sender: {self.sender_email}")
            logger.info(f"Receiver: {self.receiver_email}")
    
    def _validate_config(self):
        """Validate email configuration."""
        required_fields = ['SMTP_SENDER', 'SMTP_RECEIVER']
        missing_fields = []
        
        for field in required_fields:
            if not os.getenv(field):
                missing_fields.append(field)
        
        if not self.mock_mode and not self.smtp_password:
            missing_fields.append('SMTP_PASSWORD')
        
        if missing_fields:
            raise EmailServiceError(f"Missing required email configuration: {', '.join(missing_fields)}")
    
    def _create_backup_summary_html(self, backup_results: Dict[str, Any]) -> str:
        """Create HTML content for backup summary email."""
        
        # HTML template for backup summary
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }
        .status-success { color: #27ae60; font-weight: bold; }
        .status-failed { color: #e74c3c; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-card { background-color: #ecf0f1; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #2c3e50; }
        .summary-card .number { font-size: 24px; font-weight: bold; color: #3498db; }
        .table-results { margin: 20px 0; }
        .table-results table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table-results th, .table-results td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table-results th { background-color: #34495e; color: white; }
        .table-results tr:nth-child(even) { background-color: #f8f9fa; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d; font-size: 12px; }
        .error-details { background-color: #fdf2f2; border: 1px solid #f5c6cb; padding: 15px; border-radius: 6px; margin: 10px 0; }
        .success-icon { color: #27ae60; }
        .error-icon { color: #e74c3c; }
        .warning-icon { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TNGD Backup System Report</h1>
            <p>Backup completed on {{ timestamp }}</p>
            <p class="{% if overall_status == 'completed' %}status-success{% else %}status-failed{% endif %}">
                Status: {{ overall_status.upper() }}
            </p>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Dates Processed</h3>
                <div class="number">{{ total_dates }}</div>
            </div>
            <div class="summary-card">
                <h3>Tables Processed</h3>
                <div class="number">{{ total_tables }}</div>
            </div>
            <div class="summary-card">
                <h3>Successful</h3>
                <div class="number">{{ total_successful }}</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number">{{ total_failed }}</div>
            </div>
            <div class="summary-card">
                <h3>No Data</h3>
                <div class="number">{{ total_no_data }}</div>
            </div>
            <div class="summary-card">
                <h3>Total Rows</h3>
                <div class="number">{{ "{:,}".format(total_rows) }}</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="number">{{ duration_minutes }}m</div>
            </div>
        </div>
        
        {% if date_results %}
        <div class="table-results">
            <h2>Detailed Results by Date</h2>
            {% for date_result in date_results %}
            <h3>Date: {{ date_result.date }}</h3>
            <table>
                <thead>
                    <tr>
                        <th>Table Name</th>
                        <th>Status</th>
                        <th>Rows</th>
                        <th>Duration</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody>
                    {% for table_result in date_result.table_results %}
                    <tr>
                        <td>{{ table_result.table_name }}</td>
                        <td>
                            {% if table_result.status == 'completed' %}
                                <span class="status-success">Completed</span>
                            {% elif table_result.status == 'no_data' %}
                                <span class="status-warning">No Data</span>
                            {% else %}
                                <span class="status-failed">Failed</span>
                            {% endif %}
                        </td>
                        <td>{{ "{:,}".format(table_result.rows) }}</td>
                        <td>{{ "%.1f"|format(table_result.duration) }}s</td>
                        <td>{{ table_result.error or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endfor %}
        </div>
        {% endif %}
        
        {% if errors %}
        <div class="error-details">
            <h3>Error Details</h3>
            {% for error in errors %}
            <p><strong>{{ error.table }}:</strong> {{ error.message }}</p>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>This report was generated automatically by the TNGD Backup System.</p>
            <p>Report generated at: {{ timestamp }}</p>
        </div>
    </div>
</body>
</html>
        """
        
        # Create Jinja2 template
        template = Template(html_template)
        
        # Prepare template data
        template_data = self._prepare_template_data(backup_results)
        
        # Render HTML
        return template.render(**template_data)
    
    def _prepare_template_data(self, backup_results: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for email template rendering."""
        
        # Calculate summary statistics
        total_successful = 0
        total_failed = 0
        total_no_data = 0
        total_rows = 0
        errors = []
        
        date_results = backup_results.get('date_results', [])
        
        for date_result in date_results:
            total_successful += date_result.get('completed', 0)
            total_failed += date_result.get('failed', 0)
            total_no_data += date_result.get('no_data', 0)
            total_rows += date_result.get('total_rows', 0)
            
            # Collect errors
            for table_result in date_result.get('table_results', []):
                if table_result.get('error'):
                    errors.append({
                        'table': table_result.get('table_name', 'Unknown'),
                        'message': table_result.get('error', 'Unknown error')
                    })
        
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'overall_status': backup_results.get('status', 'unknown'),
            'total_dates': backup_results.get('total_dates', 0),
            'total_tables': backup_results.get('total_tables', 0),
            'total_successful': total_successful,
            'total_failed': total_failed,
            'total_no_data': total_no_data,
            'total_rows': total_rows,
            'duration_minutes': round(backup_results.get('overall_duration', 0) / 60, 1),
            'date_results': date_results,
            'errors': errors[:10]  # Limit to first 10 errors
        }

    def _create_email_subject(self, backup_results: Dict[str, Any]) -> str:
        """Create email subject line based on backup results."""
        status = backup_results.get('status', 'unknown')
        total_dates = backup_results.get('total_dates', 0)

        if status == 'completed':
            # Check if there were any failures
            total_failed = sum(date_result.get('failed', 0) for date_result in backup_results.get('date_results', []))
            if total_failed > 0:
                return f"WARNING: TNGD Backup Completed with {total_failed} Failures - {total_dates} Date(s)"
            else:
                return f"SUCCESS: TNGD Backup Completed Successfully - {total_dates} Date(s)"
        elif status == 'connection_failed':
            return f"ERROR: TNGD Backup Failed - Connection Error"
        else:
            return f"ERROR: TNGD Backup Failed - {total_dates} Date(s)"

    def send_backup_summary(self, backup_results: Dict[str, Any]) -> bool:
        """
        Send backup summary email.

        Args:
            backup_results: Dictionary containing backup results and statistics

        Returns:
            True if email was sent successfully, False otherwise
        """
        try:
            logger.info("Preparing backup summary email...")

            # Create email content
            subject = self._create_email_subject(backup_results)
            html_content = self._create_backup_summary_html(backup_results)

            # Create plain text version (simplified)
            plain_text = self._create_plain_text_summary(backup_results)

            # Send email
            success = self._send_email(subject, html_content, plain_text)

            if success:
                logger.info("Backup summary email sent successfully")
            else:
                logger.error("Failed to send backup summary email")

            return success

        except Exception as e:
            logger.error(f"Error sending backup summary email: {str(e)}")
            return False

    def _create_plain_text_summary(self, backup_results: Dict[str, Any]) -> str:
        """Create plain text version of backup summary."""

        # Calculate summary statistics
        total_successful = 0
        total_failed = 0
        total_no_data = 0
        total_rows = 0

        date_results = backup_results.get('date_results', [])

        for date_result in date_results:
            total_successful += date_result.get('completed', 0)
            total_failed += date_result.get('failed', 0)
            total_no_data += date_result.get('no_data', 0)
            total_rows += date_result.get('total_rows', 0)

        duration_minutes = round(backup_results.get('overall_duration', 0) / 60, 1)

        text = f"""
TNGD Backup System Report
========================

Backup completed on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Status: {backup_results.get('status', 'unknown').upper()}

Summary:
--------
Dates Processed: {backup_results.get('total_dates', 0)}
Tables Processed: {backup_results.get('total_tables', 0)}
Successful: {total_successful}
Failed: {total_failed}
No Data: {total_no_data}
Total Rows: {total_rows:,}
Duration: {duration_minutes} minutes

This report was generated automatically by the TNGD Backup System.
        """

        return text.strip()

    def _send_email(self, subject: str, html_content: str, plain_text: str) -> bool:
        """
        Send email using SMTP.

        Args:
            subject: Email subject
            html_content: HTML email content
            plain_text: Plain text email content

        Returns:
            True if email was sent successfully, False otherwise
        """
        if self.mock_mode:
            logger.info("MOCK MODE: Email would be sent with following details:")
            logger.info(f"  From: {self.sender_email}")
            logger.info(f"  To: {self.receiver_email}")
            logger.info(f"  Subject: {subject}")
            logger.info(f"  Content length: {len(html_content)} characters")
            return True

        # Validate required fields
        if not self.sender_email or not self.receiver_email or not self.smtp_password:
            logger.error("Missing required email configuration for sending")
            return False

        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.sender_email
            msg['To'] = self.receiver_email
            msg['Subject'] = subject

            # Add plain text and HTML parts
            part1 = MIMEText(plain_text, 'plain')
            part2 = MIMEText(html_content, 'html')

            msg.attach(part1)
            msg.attach(part2)

            # Send email
            logger.info(f"Connecting to SMTP server: {self.smtp_server}:{self.smtp_port}")

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.sender_email, self.smtp_password)
                server.send_message(msg)

            logger.info("Email sent successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False

    def test_email_connection(self) -> bool:
        """
        Test email connection and configuration.

        Returns:
            True if connection test is successful, False otherwise
        """
        if self.mock_mode:
            logger.info("MOCK MODE: Email connection test skipped")
            return True

        # Validate required fields
        if not self.sender_email or not self.smtp_password:
            logger.error("Missing required email configuration for connection test")
            return False

        try:
            logger.info("Testing email connection...")

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.sender_email, self.smtp_password)

            logger.info("Email connection test successful")
            return True

        except Exception as e:
            logger.error(f"Email connection test failed: {str(e)}")
            return False

    def send_test_email(self) -> bool:
        """
        Send a test email to verify email functionality.

        Returns:
            True if test email was sent successfully, False otherwise
        """
        test_results = {
            'status': 'completed',
            'total_dates': 1,
            'total_tables': 2,
            'overall_duration': 120,
            'date_results': [{
                'date': '2025-06-26',
                'completed': 2,
                'failed': 0,
                'no_data': 0,
                'total_rows': 1500,
                'table_results': [
                    {'table_name': 'test.table.1', 'status': 'completed', 'rows': 750, 'duration': 45.2, 'error': None},
                    {'table_name': 'test.table.2', 'status': 'completed', 'rows': 750, 'duration': 52.8, 'error': None}
                ]
            }]
        }

        logger.info("Sending test email...")
        return self.send_backup_summary(test_results)
