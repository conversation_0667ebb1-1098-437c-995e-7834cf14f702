#!/usr/bin/env python3
"""
Configuration Manager Module

This module provides a centralized way to manage configuration settings
from both config.json and environment variables. It handles loading
configuration values with proper fallbacks and default values.
"""

import os
import json
import logging
from datetime import datetime
from typing import Any, Dict
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Centralized configuration manager for the application.
    Handles loading from config.json and environment variables with proper fallbacks.
    """

    def __init__(self, config_file: str = None):
        """
        Initialize the configuration manager.

        Args:
            config_file: Path to the configuration file (default: auto-detect)
        """
        # Import schema validation
        self.SCHEMA_VALIDATION_AVAILABLE = False
        self.ConfigSchema = None
        self.ConfigValidationError = None

        try:
            from .config_schema import ConfigSchema, ConfigValidationError
            self.SCHEMA_VALIDATION_AVAILABLE = True
            self.ConfigSchema = ConfigSchema
            self.ConfigValidationError = ConfigValidationError
        except ImportError:
            logger.warning("Config schema validation not available")

        # Load environment variables
        load_dotenv()

        # Auto-detect config file location if not specified
        if config_file is None:
            config_file = self._find_config_file()

        # Store the config file path
        self.config_file = config_file

        # Load configuration from file
        self.config = self._load_config_file()

        # Validate configuration if schema validation is available
        if self.SCHEMA_VALIDATION_AVAILABLE:
            self._validate_configuration()

        # Validate critical credentials on startup
        self.validate_credentials()

        # Log configuration status
        if self.config:
            logger.info(f"Configuration loaded from {config_file}")
        else:
            logger.warning(f"Could not load configuration from {config_file}, using defaults")
            self.config = self._get_default_config()

    def _validate_configuration(self) -> None:
        """Validate configuration using schema validation."""
        if not self.SCHEMA_VALIDATION_AVAILABLE or not self.ConfigSchema:
            return

        try:
            errors = self.ConfigSchema.validate_config(self.config)
            if errors:
                logger.warning(f"Configuration validation found {len(errors)} issues:")
                for error in errors:
                    logger.warning(f"  - {error.path}: {error.message}")
                    if error.expected_type:
                        logger.warning(f"    Expected: {error.expected_type}, Got: {type(error.actual_value).__name__ if error.actual_value else 'None'}")
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        if self.SCHEMA_VALIDATION_AVAILABLE and self.ConfigSchema:
            return self.ConfigSchema.get_default_config()
        else:
            # Fallback minimal config
            return {
                "system": {"environment": "development", "version": "2.0.0"},
                "data_source": {"provider": "devo", "connection": {}},
                "processing": {"chunking": {}, "deduplication": {}},
                "backup_workflows": {},
                "error_handling": {"retry_strategy": {}, "failure_recovery": {}},
                "resource_management": {"memory": {}, "cpu": {}, "disk": {}},
                "storage": {"provider": "oss", "paths": {}, "compression": {}},
                "observability": {"logging": {}}
            }

    def _find_config_file(self) -> str:
        """
        Auto-detect the config file location.

        Returns:
            Path to the config file
        """
        # Try multiple possible locations for config.json
        config_paths = [
            'config/config.json',  # New organized location
            'config.json'          # Legacy location for backward compatibility
        ]

        for config_path in config_paths:
            if os.path.exists(config_path):
                logger.info(f"Found config file at: {config_path}")
                return config_path

        # Default to new organized location even if it doesn't exist
        logger.warning("No config file found, using default location: config/config.json")
        return 'config/config.json'

    def _load_config_file(self) -> Dict[str, Any]:
        """
        Load configuration from the config file.

        Returns:
            Dictionary with configuration values or empty dict if file not found
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            else:
                logger.warning(f"Configuration file not found: {self.config_file}")
                return {}
        except Exception as e:
            logger.error(f"Error loading configuration file: {str(e)}")
            return {}

    def get(self, section: str, key: str, default: Any = None, value_type: Any = None) -> Any:
        """
        Get a configuration value from the specified section and key with type validation.

        Args:
            section: Configuration section (e.g., 'system', 'storage')
            key: Configuration key within the section
            default: Default value if the key is not found
            value_type: Expected type of the value (e.g., int, str, bool)
                        If provided, will attempt to convert the value to this type

        Returns:
            Configuration value (converted to the specified type if applicable) or default if not found
        """
        # Try to get from config file
        try:
            value = self.config.get(section, {}).get(key)

            # If value not found in config, try default config
            if value is None:
                default_config = self._get_default_config()
                value = default_config.get(section, {}).get(key)

            # If still not found, use the provided default
            if value is None:
                return default

            # If a type is specified, try to convert the value
            if value_type is not None:
                try:
                    # Special handling for boolean values
                    if value_type is bool and isinstance(value, str):
                        return value.lower() in ('true', 'yes', '1', 'y', 'on')
                    # Convert to the specified type
                    return value_type(value)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Failed to convert config value '{value}' to {value_type.__name__}: {str(e)}")
                    return default

            return value

        except (KeyError, AttributeError) as e:
            logger.debug(f"Error retrieving config value {section}.{key}: {str(e)}")
            return default

    def get_env(self, key: str, default: Any = None) -> Any:
        """
        Get a value from environment variables.

        Args:
            key: Environment variable name
            default: Default value if the variable is not found

        Returns:
            Environment variable value or default if not found
        """
        return os.getenv(key, default)

    def set(self, section: str, key: str, value: Any) -> bool:
        """
        Set a configuration value in the specified section and key.

        Args:
            section: Configuration section (e.g., 'backup', 'storage')
            key: Configuration key within the section
            value: Value to set

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure the section exists
            if section not in self.config:
                self.config[section] = {}

            # Set the value
            self.config[section][key] = value
            return True
        except Exception as e:
            logger.error(f"Error setting config value {section}.{key}: {str(e)}")
            return False

    def save(self) -> bool:
        """
        Save the current configuration to the config file.

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration file: {str(e)}")
            return False

    def get_backup_settings(self) -> Dict[str, Any]:
        """
        Get all backup settings.

        Returns:
            Dictionary with backup settings
        """
        # Map old backup settings to new structure
        backup_workflows = self.config.get('backup_workflows', {})
        processing = self.config.get('processing', {})
        error_handling = self.config.get('error_handling', {})

        # Return combined settings for backward compatibility
        return {
            **backup_workflows,
            **processing,
            **error_handling
        }

    def get_storage_settings(self) -> Dict[str, Any]:
        """
        Get all storage settings.

        Returns:
            Dictionary with storage settings
        """
        return self.config.get('storage', {})

    def get_oss_credentials(self) -> Dict[str, str]:
        """
        Get OSS credentials from environment variables.

        Returns:
            Dictionary with OSS credentials
        """
        return {
            'access_key_id': self.get_env('OSS_ACCESS_KEY_ID'),
            'access_key_secret': self.get_env('OSS_ACCESS_KEY_SECRET'),
            'endpoint': self.get_env('OSS_ENDPOINT'),
            'bucket': self.get_env('OSS_BUCKET_NAME'),  # Fixed: Use OSS_BUCKET_NAME to match .env file
            'prefix': self.get_env('OSS_PREFIX', 'devo/backups/')
        }

    def get_email_settings(self) -> Dict[str, Any]:
        """
        Get email notification settings from environment variables.

        Returns:
            Dictionary with email settings
        """
        # Get email settings from environment variables
        smtp_server = self.get_env('SMTP_SERVER')
        smtp_port = self.get_env('SMTP_PORT')
        smtp_sender = self.get_env('SMTP_SENDER')
        smtp_receiver = self.get_env('SMTP_RECEIVER')
        smtp_password = self.get_env('SMTP_PASSWORD')

        # SECURITY FIX: Remove credential logging - never log password information
        # Check if SMTP is configured without exposing credentials
        if smtp_password:
            logger.info("SMTP authentication configured. Using secure credential handling.")
        else:
            logger.warning("SMTP password not configured. Email notifications may not work.")

        # Get mock mode setting (defaults to True for safety)
        mock_mode = self.get_env('SMTP_MOCK_MODE', 'true').lower() in ('true', '1', 'yes', 'on')

        # Return the settings
        return {
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'smtp_username': smtp_sender,  # Use sender email as username
            'smtp_password': smtp_password,
            'email_from': smtp_sender,
            'email_to': smtp_receiver,
            'mock_mode': mock_mode
        }

    def validate_credentials(self) -> None:
        """
        Validate critical credentials on startup.

        Raises:
            ValueError: If critical credentials are missing or invalid
        """
        logger = logging.getLogger(__name__)
        missing_credentials = []

        # Check Devo API credentials
        devo_key = self.get_env('DEVO_API_KEY')
        devo_secret = self.get_env('DEVO_API_SECRET')

        if not devo_key:
            missing_credentials.append('DEVO_API_KEY')
        elif len(devo_key.strip()) < 10:  # Basic validation
            logger.warning("DEVO_API_KEY appears to be too short")

        if not devo_secret:
            missing_credentials.append('DEVO_API_SECRET')
        elif len(devo_secret.strip()) < 10:  # Basic validation
            logger.warning("DEVO_API_SECRET appears to be too short")

        # Check OSS credentials
        oss_access_key = self.get_env('OSS_ACCESS_KEY_ID')
        oss_secret_key = self.get_env('OSS_ACCESS_KEY_SECRET')
        oss_endpoint = self.get_env('OSS_ENDPOINT')
        oss_bucket = self.get_env('OSS_BUCKET_NAME')

        if not oss_access_key:
            missing_credentials.append('OSS_ACCESS_KEY_ID')
        if not oss_secret_key:
            missing_credentials.append('OSS_ACCESS_KEY_SECRET')
        if not oss_endpoint:
            missing_credentials.append('OSS_ENDPOINT')
        if not oss_bucket:
            missing_credentials.append('OSS_BUCKET_NAME')

        # Check SMTP credentials (optional but warn if incomplete)
        # Note: We use SMTP_SENDER as the username, not a separate SMTP_USERNAME
        smtp_sender = self.get_env('SMTP_SENDER')
        smtp_password = self.get_env('SMTP_PASSWORD')

        if smtp_sender and not smtp_password:
            logger.warning("SMTP_SENDER provided but SMTP_PASSWORD is missing")
        elif smtp_password and not smtp_sender:
            logger.warning("SMTP_PASSWORD provided but SMTP_SENDER is missing")

        # Report results
        if missing_credentials:
            error_msg = f"Missing critical credentials: {', '.join(missing_credentials)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        else:
            logger.info("All critical credentials validated successfully")

        # Validate credential format (basic checks)
        self._validate_credential_formats()

    def _validate_credential_formats(self) -> None:
        """Validate credential formats and patterns."""
        logger = logging.getLogger(__name__)

        # Validate OSS endpoint format
        oss_endpoint = self.get_env('OSS_ENDPOINT', '')
        if oss_endpoint and not (oss_endpoint.startswith('http://') or oss_endpoint.startswith('https://')):
            logger.warning("OSS_ENDPOINT should start with http:// or https://")

        # Validate email format if provided
        email_from = self.get_env('SMTP_SENDER')
        if email_from and '@' not in email_from:
            logger.warning("SMTP_SENDER does not appear to be a valid email address")

        logger.info("Credential format validation completed")

    def rotate_credentials(self, credential_updates: Dict[str, str]) -> bool:
        """
        Support credential rotation by updating environment variables.

        Args:
            credential_updates: Dictionary of credential name -> new value

        Returns:
            True if rotation was successful, False otherwise
        """
        logger = logging.getLogger(__name__)

        try:
            # Validate new credentials before applying
            for cred_name, new_value in credential_updates.items():
                if not new_value or len(new_value.strip()) < 5:
                    raise ValueError(f"Invalid new credential value for {cred_name}")

            # Apply credential updates
            for cred_name, new_value in credential_updates.items():
                os.environ[cred_name] = new_value
                logger.info(f"[SUCCESS] Rotated credential: {cred_name}")

            # Re-validate all credentials after rotation
            self.validate_credentials()

            logger.info(f"[SUCCESS] Successfully rotated {len(credential_updates)} credentials")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Credential rotation failed: {e}")
            return False

    def check_credential_age(self) -> Dict[str, Any]:
        """
        Check the age of credentials (if timestamp info is available).

        Returns:
            Dictionary with credential age information
        """
        logger = logging.getLogger(__name__)

        # This is a placeholder for credential age checking
        # In a real implementation, you might store credential creation timestamps
        # in a secure metadata store or check with the credential provider

        credential_info = {
            'last_rotation_check': datetime.now().isoformat(),
            'rotation_recommended': False,
            'credentials_checked': [
                'DEVO_API_KEY',
                'DEVO_API_SECRET',
                'OSS_ACCESS_KEY_ID',
                'OSS_ACCESS_KEY_SECRET'
            ]
        }

        logger.info("Credential age check completed")
        return credential_info

    def get_credential_security_status(self) -> Dict[str, Any]:
        """
        Get overall credential security status.

        Returns:
            Dictionary with security status information
        """
        logger = logging.getLogger(__name__)

        try:
            # Re-validate credentials
            self.validate_credentials()

            # Check credential age
            age_info = self.check_credential_age()

            status = {
                'validation_status': 'PASSED',
                'validation_timestamp': datetime.now().isoformat(),
                'credential_age_info': age_info,
                'security_recommendations': []
            }

            # Add security recommendations
            if not self.get_env('SMTP_PASSWORD'):
                status['security_recommendations'].append('Consider configuring SMTP for notifications')

            logger.info("Credential security status check completed")
            return status

        except Exception as e:
            logger.error(f"[ERROR] Credential security status check failed: {e}")
            return {
                'validation_status': 'FAILED',
                'validation_timestamp': datetime.now().isoformat(),
                'error': str(e),
                'security_recommendations': ['Fix credential validation errors']
            }


