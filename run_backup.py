#!/usr/bin/env python3
"""
TNGD Backup System - Main Runner Script

Professional wrapper script for running the TNGD backup system with
proper error handling, monitoring, and user-friendly interface.

Usage:
    python run_backup.py                    # Today's data
    python run_backup.py 2025-03-26         # Single date
    python run_backup.py 2025-03-26 2025-03-31  # Date range
"""

import os
import sys
import subprocess
import time
import signal
from datetime import datetime
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


class BackupRunner:
    """Professional backup runner with monitoring and error handling."""

    def __init__(self):
        """Initialize backup runner."""
        self.project_root = project_root
        self.monitor_process = None
        self.backup_process = None

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        self._cleanup_processes()
        sys.exit(0)

    def check_prerequisites(self):
        """Check if all required files and dependencies exist."""
        print("🔍 Checking prerequisites...")

        required_files = [
            "src/tngd_backup/main.py",
            "src/tngd_backup/core/backup_engine.py",
            "config/default.json"
        ]

        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)

        if missing_files:
            print("❌ Missing required files:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            return False

        print("✅ All required files found")
        return True

    def start_monitoring(self):
        """Start resource monitoring in background."""
        try:
            print("🔍 Starting resource monitoring...")

            monitor_script = self.project_root / "src" / "tngd_backup" / "utils" / "monitoring.py"
            if monitor_script.exists():
                self.monitor_process = subprocess.Popen(
                    [sys.executable, str(monitor_script), "--monitor", "--interval", "30"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=str(self.project_root)
                )
                print(f"✅ Resource monitor started (PID: {self.monitor_process.pid})")
            else:
                print("⚠️ Resource monitor script not found, continuing without monitoring")

        except Exception as e:
            print(f"⚠️ Could not start resource monitor: {e}")

    def run_backup(self, dates_args):
        """Run the backup system."""
        try:
            print("🚀 Starting TNGD Backup System v2.0...")

            # Build command to run the main backup module
            cmd = [
                sys.executable,
                "-m", "tngd_backup.main"
            ] + dates_args

            print(f"Command: {' '.join(cmd)}")
            print("=" * 60)

            # Set up environment with proper Python path
            env = os.environ.copy()
            src_path = str(self.project_root / "src")
            if "PYTHONPATH" in env:
                env["PYTHONPATH"] = f"{src_path}{os.pathsep}{env['PYTHONPATH']}"
            else:
                env["PYTHONPATH"] = src_path

            # Run backup with real-time output
            self.backup_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=str(self.project_root),
                env=env
            )

            # Stream output in real-time
            for line in iter(self.backup_process.stdout.readline, ''):
                print(line.rstrip())

            # Wait for completion
            return_code = self.backup_process.wait()

            print("=" * 60)
            if return_code == 0:
                print("✅ Backup completed successfully!")
            else:
                print("❌ Backup failed!")

            return return_code == 0

        except KeyboardInterrupt:
            print("\n⚠️ Backup interrupted by user")
            if self.backup_process:
                self.backup_process.terminate()
            return False
        except Exception as e:
            print(f"❌ Error running backup: {e}")
            return False

    def _cleanup_processes(self):
        """Clean up background processes."""
        if self.monitor_process:
            try:
                self.monitor_process.terminate()
                self.monitor_process.wait(timeout=5)
                print("🔍 Resource monitor stopped")
            except:
                try:
                    self.monitor_process.kill()
                except:
                    pass

        if self.backup_process:
            try:
                self.backup_process.terminate()
                self.backup_process.wait(timeout=10)
                print("🛑 Backup process stopped")
            except:
                try:
                    self.backup_process.kill()
                except:
                    pass

    def show_usage(self):
        """Show usage instructions."""
        print("""
🔧 TNGD Backup System v2.0 - Professional Runner
================================================

Usage:
    python run_backup.py [OPTIONS] [DATES...]

Date Arguments:
    (none)                      Backup today's data
    YYYY-MM-DD                  Backup specific date
    YYYY-MM-DD YYYY-MM-DD       Backup date range (inclusive)

Options:
    -h, --help                  Show this help message
    --config ENV                Use specific config (default, production, development)
    --check-only                Only check system readiness
    --no-monitor                Skip resource monitoring

Examples:
    python run_backup.py
    python run_backup.py 2025-03-26
    python run_backup.py 2025-03-26 2025-03-31
    python run_backup.py --config production 2025-03-26

Features:
    ✓ Professional project structure
    ✓ Resource monitoring and alerts
    ✓ Graceful shutdown handling
    ✓ Real-time progress display
    ✓ Comprehensive error handling
    ✓ Multiple environment configs
        """)

    def run(self, args):
        """Main run method."""
        # Parse arguments
        if len(args) > 0 and args[0] in ['-h', '--help', 'help']:
            self.show_usage()
            return 0

        # Check prerequisites
        if not self.check_prerequisites():
            print("\n❌ Prerequisites check failed!")
            print("Please ensure all required files are present.")
            return 1

        # Parse config option
        config_env = None
        dates_args = []
        no_monitor = False
        check_only = False

        i = 0
        while i < len(args):
            if args[i] == '--config' and i + 1 < len(args):
                config_env = args[i + 1]
                i += 2
            elif args[i] == '--no-monitor':
                no_monitor = True
                i += 1
            elif args[i] == '--check-only':
                check_only = True
                i += 1
            else:
                dates_args.append(args[i])
                i += 1

        # Add config to dates_args if specified
        if config_env:
            config_file = self.project_root / "config" / f"{config_env}.json"
            if config_file.exists():
                dates_args = ["--config", str(config_file)] + dates_args
            else:
                print(f"❌ Config file not found: {config_file}")
                return 1

        # Add check-only flag
        if check_only:
            dates_args = ["--check-only"] + dates_args

        try:
            # Start monitoring unless disabled
            if not no_monitor and not check_only:
                self.start_monitoring()

            # Run backup
            success = self.run_backup(dates_args)

            return 0 if success else 1

        finally:
            # Cleanup
            self._cleanup_processes()


def main():
    """Main entry point."""
    runner = BackupRunner()
    return runner.run(sys.argv[1:])


if __name__ == "__main__":
    sys.exit(main())
