#!/usr/bin/env python3
"""
Test script to send a test email with the updated template.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from tngd_backup.core.email_service import EmailService

def main():
    """Send a test email to verify the template changes."""
    print("Testing email template with PNG icon...")

    # Create email service
    email_service = EmailService()
    
    # Create test backup results with sample data
    test_results = {
        'status': 'completed',
        'total_dates': 1,
        'total_tables': 3,
        'overall_duration': 180,  # 3 minutes
        'date_results': [{
            'date': '2025-07-01',
            'completed': 2,
            'failed': 1,
            'no_data': 0,
            'total_rows': 15000,
            'table_results': [
                {
                    'table_name': 'my.app.tngd.waf',
                    'status': 'completed',
                    'rows': 8500,
                    'duration': 65.2,
                    'error': None
                },
                {
                    'table_name': 'my.app.tngd.actiontraillinux',
                    'status': 'completed',
                    'rows': 6500,
                    'duration': 45.8,
                    'error': None
                },
                {
                    'table_name': 'cloud.office365.management.exchange',
                    'status': 'failed',
                    'rows': 0,
                    'duration': 12.5,
                    'error': 'Connection timeout'
                }
            ]
        }]
    }
    
    print("Sending test email...")
    success = email_service.send_backup_summary(test_results)
    
    if success:
        print("✅ Test email sent successfully!")
        print("Check your email to see the updated template with:")
        print("  - Centered header with PNG icon from icon.png")
        print("  - Centered footer")
        print("  - Updated footer text: 'Techlab Automation Team'")
    else:
        print("❌ Failed to send test email")
        print("Check your email configuration in .env file")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
