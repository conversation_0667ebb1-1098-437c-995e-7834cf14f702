#!/usr/bin/env python3
"""
Test email with embedded PNG logo using Content-ID.
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from tngd_backup.core.email_service import EmailService

def main():
    """Send a test email with embedded PNG logo."""
    print("Testing email with embedded PNG logo...")
    
    # Check if icon.png exists
    icon_path = Path("icon.png")
    if not icon_path.exists():
        print("❌ Error: icon.png not found in project root")
        print("Please make sure icon.png exists in the same directory as this script")
        return 1
    
    print(f"✅ Found icon.png ({icon_path.stat().st_size} bytes)")
    
    # Create email service
    email_service = EmailService()
    
    # Create test backup results
    test_results = {
        'status': 'completed',
        'total_dates': 1,
        'total_tables': 3,
        'overall_duration': 180,  # 3 minutes
        'date_results': [{
            'date': '2025-07-01',
            'completed': 2,
            'failed': 1,
            'no_data': 0,
            'total_rows': 15000,
            'table_results': [
                {
                    'table_name': 'my.app.tngd.waf',
                    'status': 'completed',
                    'rows': 8500,
                    'duration': 65.2,
                    'error': None
                },
                {
                    'table_name': 'my.app.tngd.actiontraillinux',
                    'status': 'completed',
                    'rows': 6500,
                    'duration': 45.8,
                    'error': None
                },
                {
                    'table_name': 'cloud.office365.management.exchange',
                    'status': 'failed',
                    'rows': 0,
                    'duration': 12.5,
                    'error': 'Connection timeout'
                }
            ]
        }]
    }
    
    print("Sending test email with larger PNG logo...")
    success = email_service.send_backup_summary(test_results)

    if success:
        print("✅ Test email sent successfully!")
        print("Check your Gmail - your PNG logo should now be larger and more visible!")
        print("Features:")
        print("  - 🖼️ Your PNG logo (60px height, auto width, max 200px)")
        print("  - Centered header layout")
        print("  - Centered footer")
        print("  - Updated footer text: 'Techlab Automation Team'")
    else:
        print("❌ Failed to send test email")
        print("Check your email configuration in .env file")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
