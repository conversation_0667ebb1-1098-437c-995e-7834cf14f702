<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; text-align: center; }
        .header-icon { width: 32px; height: 32px; margin-bottom: 10px; }
        .status-success { color: #27ae60; font-weight: bold; }
        .status-failed { color: #e74c3c; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px; }
        .summary-card { background-color: #ecf0f1; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #2c3e50; font-size: 14px; }
        .summary-card .number { font-size: 24px; font-weight: bold; color: #3498db; }
        .table-results { margin: 20px 0; }
        .table-results h3 { color: #2c3e50; margin-bottom: 10px; }
        .table-results table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table-results th, .table-results td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table-results th { background-color: #34495e; color: white; }
        .table-results tr:nth-child(even) { background-color: #f8f9fa; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d; font-size: 12px; text-align: center; }
        .error-details { background-color: #fdf2f2; border: 1px solid #f5c6cb; padding: 15px; border-radius: 6px; margin: 10px 0; }
        .success-icon { color: #27ae60; }
        .error-icon { color: #e74c3c; }
        .warning-icon { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: inline-block; width: 40px; height: 40px; background: linear-gradient(135deg, #3498db, #2980b9); border-radius: 8px; margin-bottom: 15px; position: relative; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <span style="color: white; font-weight: bold; font-size: 20px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-family: 'Arial', sans-serif;">🔧</span>
            </div>
            <h1>TNGD Backup System Report</h1>
            <p>Backup completed on 2025-07-01 23:16:39</p>
            <p class="status-success">
                Status: COMPLETED
            </p>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Dates Processed</h3>
                <div class="number">1</div>
            </div>
            <div class="summary-card">
                <h3>Tables Processed</h3>
                <div class="number">3</div>
            </div>
            <div class="summary-card">
                <h3>Successful</h3>
                <div class="number">2</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number">1</div>
            </div>
            <div class="summary-card">
                <h3>No Data</h3>
                <div class="number">0</div>
            </div>
            <div class="summary-card">
                <h3>Total Rows</h3>
                <div class="number">15,000</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="number">3.0m</div>
            </div>
        </div>
        
        <div class="table-results">
            <h3>📅 Results for 2025-07-01</h3>
            <table>
                <thead>
                    <tr>
                        <th>Table Name</th>
                        <th>Status</th>
                        <th>Rows</th>
                        <th>Duration (s)</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>my.app.tngd.waf</td>
                        <td><span class="success-icon">✅</span> completed</td>
                        <td>8,500</td>
                        <td>65.2</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>my.app.tngd.actiontraillinux</td>
                        <td><span class="success-icon">✅</span> completed</td>
                        <td>6,500</td>
                        <td>45.8</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td>cloud.office365.management.exchange</td>
                        <td><span class="error-icon">❌</span> failed</td>
                        <td>0</td>
                        <td>12.5</td>
                        <td>Connection timeout</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="error-details">
            <h3>Error Details</h3>
            <p><strong>cloud.office365.management.exchange:</strong> Connection timeout</p>
        </div>
        
        <div class="footer">
            <p>This report was generated automatically by Techlab Automation Team.</p>
            <p>Report generated at: 2025-07-01 23:16:39</p>
        </div>
    </div>
</body>
</html>
